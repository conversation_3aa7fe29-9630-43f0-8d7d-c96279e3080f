import { Button } from '@/components/ui/button';

interface CheckoutButtonProps {
  onSubmit: (event: React.FormEvent) => void;
  isLoading?: boolean;
  disabled?: boolean;
  text?: string;
}

const CheckoutButton = ({ 
  onSubmit, 
  isLoading = false, 
  disabled = false,
  text = "Proceed to checkout"
}: CheckoutButtonProps) => {
  return (
    <div className="flex justify-end pt-6">
      <Button 
        type="submit"
        size="lg"
        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-12 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 w-full md:w-1/2"
        onClick={onSubmit}
        disabled={disabled || isLoading}
      >
        {isLoading ? "Processing..." : text}
      </Button>
    </div>
  );
};

export default CheckoutButton;
