import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { CalendarIcon, MapPinIcon, ClockIcon, UsersIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBookingForm, type UseBookingFormProps } from './hooks/useBookingForm';
import { Hotel } from '@/features/hotels/types/hotels.types';
import { 
  OriginSelect, 
  DestinationSearch, 
  DatePicker, 
  TimeSelect, 
  PassengerSelect,
  RoundTripToggle 
} from './components';

export interface BookingFormData {
  from: string;
  to: string;
  date: Date | null;
  time: string;
  returnDate: Date | null;
  returnTime: string;
  adults: string;
  kids: string;
  roundTrip: boolean;
  passengers?: string;
}

export type BookingFormVariant = 'enhanced' | 'vertical' | 'horizontal';

export interface BookingFormProps {
  variant?: BookingFormVariant;
  onFormChange?: (data: BookingFormData) => void;
  initialData?: {
    from?: string;
    to?: string;
    date?: Date | null;
    time?: string;
    returnDate?: Date | null;
    returnTime?: string;
    adults?: string;
    kids?: string;
    passengers?: string;
    roundTrip?: boolean;
    selectedHotel?: Hotel;
  };
  showContactInfo?: boolean;
  showSpecialOffer?: boolean;
  autoNavigate?: boolean;
  className?: string;
  destinationZoneId?: number;
  availableHotels?: Hotel[];
  searchMode?: 'search' | 'dropdown' | 'both';
}

// Tipos para el componente de campos comunes
interface FormFieldsProps {
  variant: BookingFormVariant;
  formState: {
    from: string;
    to: string;
    date: Date | null;
    time: string;
    returnDate: Date | null;
    returnTime: string;
    adults: string;
    kids: string;
    roundTrip: boolean;
    passengers?: string;
    selectedHotel?: Hotel | null;
    errors: {
      from?: boolean;
      to?: boolean;
      date?: boolean;
      time?: boolean;
      returnDate?: boolean;
      returnTime?: boolean;
      adults?: boolean;
      kids?: boolean;
    };
  };
  handlers: {
    updateField: (field: string, value: string | Date | boolean | null) => void;
    handleHotelChange: (value: string, hotel?: Hotel) => void;
    handleFromChange: (value: string) => void;
  };
  airportData: {
    airportOptions: Array<{ value: string; label: string; }>;
    loading: boolean;
  };
  destinationZoneId?: number;
  availableHotels?: Hotel[];
  searchMode?: 'search' | 'dropdown' | 'both';
  styleClasses: {
    label: string;
    input: string;
  };
}

// Componente de campos comunes para reutilizar
const FormFields = ({ 
  variant,
  formState, 
  handlers, 
  airportData, 
  destinationZoneId, 
  availableHotels, 
  searchMode,
  styleClasses 
}: FormFieldsProps) => (
  <>
    {/* Pickup Location */}
    <div className="space-y-2">
      <Label className={styleClasses.label}>
        <MapPinIcon className="w-4 h-4 inline mr-2" />
        Pickup Location
      </Label>
      <OriginSelect
        value={formState.from}
        onChange={handlers.handleFromChange}
        error={formState.errors.from}
        airportOptions={airportData.airportOptions}
        loading={airportData.loading}
        className={styleClasses.input}
        errorText="Please select pickup location"
      />
    </div>

    {/* Destination */}
    <div className="space-y-2">
      <Label className={styleClasses.label}>
        <MapPinIcon className="w-4 h-4 inline mr-2" />
        Destination
      </Label>
      <DestinationSearch
        value={formState.to}
        onChange={handlers.handleHotelChange}
        error={formState.errors.to}
        className={styleClasses.input}
        errorText="Please enter destination"
        destinationZoneId={destinationZoneId}
        availableHotels={availableHotels}
        searchMode={searchMode}
        selectedHotel={formState.selectedHotel}
      />
    </div>

    {/* Date */}
    <div className="space-y-2">
      <Label className={styleClasses.label}>
        <CalendarIcon className="w-4 h-4 inline mr-2" />
        Pickup Date
      </Label>
      <DatePicker
        value={formState.date}
        onChange={(date) => handlers.updateField('date', date)}
        error={formState.errors.date}
        className={styleClasses.input}
        errorText="Please select date"
        minDate={new Date(Date.now() + 48 * 60 * 60 * 1000)} // 48 hours from now
      />
    </div>

    {/* Time - Only show if not horizontal variant */}
    {variant !== 'horizontal' && (
      <div className="space-y-2">
        <Label className={styleClasses.label}>
          <ClockIcon className="w-4 h-4 inline mr-2" />
          Pickup Time
        </Label>
        <TimeSelect
          value={formState.time}
          onChange={(value) => handlers.updateField('time', value)}
          error={formState.errors.time}
          className={styleClasses.input}
          errorText="Please select time"
        />
      </div>
    )}

    {/* Return Date - Only show if roundTrip is true */}
    {formState.roundTrip && (
      <>
        <div className="space-y-2">
          <Label className={styleClasses.label}>
            <CalendarIcon className="w-4 h-4 inline mr-2" />
            Return Date
          </Label>
           <DatePicker
             value={formState.returnDate}
             onChange={(date) => handlers.updateField('returnDate', date)}
             error={formState.errors.returnDate}
             className={styleClasses.input}
             errorText="Please select return date"
             minDate={formState.date ? new Date(formState.date.getTime() + 24 * 60 * 60 * 1000) : new Date(Date.now() + 48 * 60 * 60 * 1000)}
           />
        </div>

        {/* Return Time - Only show if not horizontal variant */}
        {variant !== 'horizontal' && (
          <div className="space-y-2">
            <Label className={styleClasses.label}>
              <ClockIcon className="w-4 h-4 inline mr-2" />
              Return Time
            </Label>
            <TimeSelect
              value={formState.returnTime}
              onChange={(value) => handlers.updateField('returnTime', value)}
              error={formState.errors.returnTime}
              className={styleClasses.input}
              errorText="Please select return time"
            />
          </div>
        )}
      </>
    )}
  </>
);

const BookingForm = ({ 
  variant = 'enhanced',
  onFormChange, 
  initialData,
  showContactInfo = true,
  showSpecialOffer = true,
  autoNavigate = false,
  className,
  destinationZoneId,
  availableHotels,
  searchMode = 'both'
}: BookingFormProps) => {
  const { formState, airportData, handlers } = useBookingForm({ 
    onFormChange, 
    initialData,
    formType: variant,
    autoNavigate 
  });

  const handleFormSubmit = (event: React.FormEvent) => {
    const formData = handlers.handleSubmit(event);
    
    if (formData && onFormChange && !autoNavigate) {
      onFormChange({
        from: formState.from,
        to: formState.to,
        date: formState.date,
        time: formState.time,
        returnDate: formState.returnDate,
        returnTime: formState.returnTime,
        adults: formState.adults,
        kids: formState.kids,
        passengers: formState.passengers,
        roundTrip: formState.roundTrip
      });
    }
  };

  // Enhanced Variant - Card style with gradient background
  if (variant === 'enhanced') {
    const styleClasses = {
      label: "text-white text-sm font-medium",
      input: "bg-white/10 border-white/20 text-white placeholder:text-white/70"
    };

    return (
      <Card className={cn("bg-gradient-to-br from-primary to-accent text-white shadow-2xl", className)}>
        <CardContent className="p-4 md:p-8">
          <form className="space-y-6" onSubmit={handleFormSubmit}>
            {/* Header */}
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2 flex items-center justify-center gap-2">
                <CalendarIcon className="w-6 h-6" />
                Book Your Transfer
              </h2>
              <p className="text-white/90 text-sm">Choose your perfect transportation solution</p>
            </div>

            {/* Round Trip Toggle */}
            <div className="p-4 bg-white/10 rounded-lg backdrop-blur-sm">
              <RoundTripToggle
                value={formState.roundTrip}
                onChange={(checked) => handlers.updateField('roundTrip', checked)}
                label="Round Trip"
                className="[&>span]:text-white [&>span]:font-medium"
              />
            </div>

            {/* Form Fields */}
            <div className="space-y-1">
              <FormFields
                variant={variant}
                formState={formState}
                handlers={handlers}
                airportData={{
                  ...airportData,
                  airportOptions: airportData.airportOptions
                    .filter(opt => typeof opt.value === 'string' && typeof opt.label === 'string')
                    .map(opt => ({
                      value: String(opt.value),
                      label: String(opt.label)
                    }))
                }}
                destinationZoneId={destinationZoneId}
                availableHotels={availableHotels}
                searchMode={searchMode}
                styleClasses={styleClasses}
              />

              {/* Passengers */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4">
                <div className="space-y-1">
                  <Label className="text-white text-sm font-medium">
                    <UsersIcon className="w-4 h-4 inline mr-2" />
                    Adults
                  </Label>
                  <PassengerSelect
                    value={formState.adults}
                    onChange={(value) => handlers.updateField('adults', value)}
                    type="adults"
                    className="bg-white/10 border-white/20 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-white text-sm font-medium">
                    <UsersIcon className="w-4 h-4 inline mr-2" />
                    Kids
                  </Label>
                  <PassengerSelect
                    value={formState.kids}
                    onChange={(value) => handlers.updateField('kids', value)}
                    type="kids"
                    className="bg-white/10 border-white/20 text-white"
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <Button 
              type="submit"
              className="w-full bg-white text-primary hover:bg-gray-100 font-semibold py-3 text-lg"
            >
              {autoNavigate ? 'Book Now' : 'Update Booking'}
            </Button>
          </form>
        </CardContent>
      </Card>
    );
  }

  // Vertical Variant - Simple stacked layout
  if (variant === 'vertical') {
    const styleClasses = {
      label: "text-gray-700 text-sm font-medium",
      input: ""
    };

    return (
      <form className={cn("space-y-6", className)} onSubmit={handleFormSubmit}>
        {/* Header */}
        <div className="flex justify-between items-center">
          <h3 className="text-2xl font-bold text-gray-900">Book Your Transfer</h3>
          <RoundTripToggle
            value={formState.roundTrip}
            onChange={(value) => handlers.updateField('roundTrip', value)}
          />
        </div>

        {/* Form Fields */}
        <div className="space-y-4">
          <FormFields
            variant={variant}
            formState={formState}
            handlers={handlers}
            airportData={{
              ...airportData,
              airportOptions: airportData.airportOptions
                .filter(opt => typeof opt.value !== 'undefined' && typeof opt.label !== 'undefined')
                .map(opt => ({
                  value: String(opt.value),
                  label: String(opt.label)
                }))
            }}
            destinationZoneId={destinationZoneId}
            availableHotels={availableHotels}
            searchMode={searchMode}
            styleClasses={styleClasses}
          />

          {/* Passengers in grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-gray-700 text-sm font-medium">
                <UsersIcon className="w-4 h-4 inline mr-2" />
                Adults
              </Label>
              <PassengerSelect
                value={formState.adults}
                onChange={(value) => handlers.updateField('adults', value)}
                type="adults"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-gray-700 text-sm font-medium">
                <UsersIcon className="w-4 h-4 inline mr-2" />
                Kids
              </Label>
              <PassengerSelect
                value={formState.kids}
                onChange={(value) => handlers.updateField('kids', value)}
                type="kids"
              />
            </div>
          </div>

          {/* Submit Button */}
          <Button 
            type="submit"
            className="w-full bg-gradient-cabo hover:opacity-90 text-white font-semibold py-3 text-lg"
          >
            {autoNavigate ? 'Book Now' : 'Update Booking'}
          </Button>
        </div>
      </form>
    );
  }

  // Horizontal Variant - Inline compact layout
  if (variant === 'horizontal') {
    const styleClasses = {
      label: "text-primary text-sm font-medium",
      input: ""
    };

    return (
      <div className={cn("bg-white rounded-lg shadow-lg p-6 mb-4", className)}>
        <form onSubmit={handleFormSubmit}>
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold text-gray-900">Book Your Transfer</h3>
            <RoundTripToggle
              value={formState.roundTrip}
              onChange={(value) => handlers.updateField('roundTrip', value)}
            />
          </div>

          {/* Horizontal Fields */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <FormFields
              variant={variant}
              formState={formState}
              handlers={handlers}
              airportData={{
                ...airportData,
                airportOptions: airportData.airportOptions
                  .filter(opt => typeof opt.value !== 'undefined' && typeof opt.label !== 'undefined')
                  .map(opt => ({
                    value: String(opt.value),
                    label: String(opt.label)
                  }))
              }}
              destinationZoneId={destinationZoneId}
              availableHotels={availableHotels}
              searchMode={searchMode}
              styleClasses={styleClasses}
            />

            {/* Submit Button */}
            <div className="flex flex-col">
              <div className="h-6 mb-2"></div>
              <Button 
                type="submit"
                className="bg-gradient-cabo hover:opacity-90 text-white font-semibold h-12 px-8 w-full"
              >
                Book Now
              </Button>
              <div className="h-5 mt-1"></div>
            </div>
          </div>

          {/* Info Note */}
          {/* <div className="text-center">
            <p className="text-sm text-gray-600">
              Vehicle capacity: Up to 14 passengers (select vehicle type in next step)
            </p>
          </div> */}
        </form>
      </div>
    );
  }

  return null;
};

export default BookingForm;
