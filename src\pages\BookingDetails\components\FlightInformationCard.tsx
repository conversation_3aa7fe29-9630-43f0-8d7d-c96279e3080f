import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PlaneIcon } from 'lucide-react';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { TimeSelect } from '@/components/booking-form/components';
import { useBooking } from '@/context/BookingContext';
import { useAirlines } from '@/features/airlines/hooks/useAirlines';
import type { BookingDetailsFormData } from '../schemas/bookingDetailsSchema';

interface FlightInformationCardProps {
  control: Control<BookingDetailsFormData>;
  errors: FieldErrors<BookingDetailsFormData>;
}

const FlightInformationCard = ({ control, errors }: FlightInformationCardProps) => {
  const { state, setFlightInfo } = useBooking();
  const { data: airlines, loading: airlinesLoading } = useAirlines();

  // Función para actualizar la información de vuelo en el contexto
  const updateFlightInfo = (field: string, value: string) => {
    const currentFlightInfo = state.flightInfo || {};
    const updatedFlightInfo = {
      ...currentFlightInfo,
      [field]: value
    };
    setFlightInfo(updatedFlightInfo);
  };

  return (
    <Card className="bg-white shadow-lg border-0 overflow-hidden group">
      <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 border-b border-primary/20">
        <CardTitle className="flex items-center gap-2 text-primary">
          <PlaneIcon className="w-5 h-5" />
          Flight Information
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-4">
        {/* Arrival Information */}
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900 flex items-center gap-2">
            <span className="text-green-600">✈️</span>
            Arrival Flight Information
          </h4>
        </div>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Arrival Time</Label>
            <Controller
              name="flightInfo.arrivalTime"
              control={control}
              render={({ field }) => (
                <TimeSelect
                  value={field.value || state.flightInfo?.arrivalTime || ''}
                  onChange={(value) => {
                    field.onChange(value);
                    updateFlightInfo('arrivalTime', value);
                  }}
                  error={!!errors.flightInfo?.arrivalTime}
                  errorText="Please select arrival time"
                />
              )}
            />
          </div>
          <div className="space-y-2">
            <Label>Arrival Airline</Label>
            <Controller
              name="flightInfo.airline"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value || state.flightInfo?.airline || ''}
                  onValueChange={(value) => {
                    field.onChange(value);
                    updateFlightInfo('airline', value);
                  }}
                  disabled={airlinesLoading}
                >
                  <SelectTrigger className={errors.flightInfo?.airline ? 'border-red-500' : ''}>
                    <SelectValue placeholder={airlinesLoading ? 'Loading airlines...' : 'Select airline'} />
                  </SelectTrigger>
                  <SelectContent>
                    {airlines?.map((airline) => (
                      <SelectItem key={airline.code_airline} value={airline.name}>
                        {airline.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
          <div className="space-y-2">
            <Label>Arrival Flight Number</Label>
            <Controller
              name="flightInfo.flightNumber"
              control={control}
              render={({ field }) => (
                <Input
                  placeholder="e.g., AA1234"
                  value={field.value || state.flightInfo?.flightNumber || ''}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    updateFlightInfo('flightNumber', e.target.value);
                  }}
                />
              )}
            />
          </div>
        </div>
        {/* Departure Information - Only show for Round Trip */}
        {state.roundTrip && (
          <>
            <div className="space-y-2 pt-4 border-t border-gray-200">
              <h4 className="font-medium text-gray-900 flex items-center gap-2">
                <span className="text-blue-600">🛫</span>
                Departure Flight Information
              </h4>
              <p className="text-sm text-gray-600">
                Information for your return flight
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Departure Time</Label>
                <Controller
                  name="flightInfo.departureTime"
                  control={control}
                  render={({ field }) => (
                    <TimeSelect
                      value={field.value || state.flightInfo?.departureTime || ''}
                      onChange={(value) => {
                        field.onChange(value);
                        updateFlightInfo('departureTime', value);
                      }}
                      error={!!errors.flightInfo?.departureTime}
                      errorText="Please select departure time"
                    />
                  )}
                />
              </div>
              <div className="space-y-2">
                <Label>Departure Airline</Label>
                <Controller
                  name="flightInfo.departureAirline"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || state.flightInfo?.departureAirline || ''}
                      onValueChange={(value) => {
                        field.onChange(value);
                        updateFlightInfo('departureAirline', value);
                      }}
                      disabled={airlinesLoading}
                    >
                      <SelectTrigger className={errors.flightInfo?.departureAirline ? 'border-red-500' : ''}>
                        <SelectValue placeholder={airlinesLoading ? 'Loading airlines...' : 'Select airline'} />
                      </SelectTrigger>
                      <SelectContent>
                        {airlines?.map((airline) => (
                          <SelectItem key={airline.code_airline} value={airline.name}>
                            {airline.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
              <div className="space-y-2">
                <Label>Departure Flight Number</Label>
                <Controller
                  name="flightInfo.departureFlightNumber"
                  control={control}
                  render={({ field }) => (
                    <Input
                      placeholder="e.g., AA1234"
                      value={field.value || state.flightInfo?.departureFlightNumber || ''}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        updateFlightInfo('departureFlightNumber', e.target.value);
                      }}
                    />
                  )}
                />
              </div>
            </div>
          </>
        )}

        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700">
            💡 Flight information helps us track your arrival{state.roundTrip ? ' and departure' : ''} and provide better service
          </p>
          {state.roundTrip && (
            <p className="text-xs text-blue-600 mt-1">
              Departure information is used for your return transfer
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default FlightInformationCard;
