import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { UsersIcon } from 'lucide-react';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import type { BookingDetailsFormData } from '../schemas/bookingDetailsSchema';

interface AdditionalItemsCardProps {
  control: Control<BookingDetailsFormData>;
  errors: FieldErrors<BookingDetailsFormData>;
}

const AdditionalItemsCard = ({ control, errors }: AdditionalItemsCardProps) => {
  return (
    <Card className="hover-scale bg-white shadow-lg border-0 overflow-hidden group">
      <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 border-b border-primary/20">
        <CardTitle className="flex items-center gap-2 text-primary">
          <UsersIcon className="w-5 h-5" />
          Additional Items
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Extra Services */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Extra Services</h4>

          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Controller
                name="additionalItems.extraServices.stopShop"
                control={control}
                render={({ field }) => (
                  <input
                    type="checkbox"
                    id="stopShop"
                    className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    checked={field.value}
                    onChange={field.onChange}
                  />
                )}
              />
              <label htmlFor="stopShop" className="text-sm text-muted-foreground">
                Stop and Shop: Grocery Store, Supermarket. (30 min) <span className="font-medium">$30.00 usd</span>
              </label>
            </div>

            <div className="flex items-center space-x-3">
              <Controller
                name="additionalItems.extraServices.golfBags"
                control={control}
                render={({ field }) => (
                  <input
                    type="checkbox"
                    id="golfBags"
                    className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    checked={field.value}
                    onChange={field.onChange}
                  />
                )}
              />
              <label htmlFor="golfBags" className="text-sm text-muted-foreground">
                Golf clubs bags <span className="font-medium">($20.00 usd per bag)</span>
              </label>
            </div>

            <div className="flex items-center space-x-3">
              <Controller
                name="additionalItems.extraServices.surfboards"
                control={control}
                render={({ field }) => (
                  <input
                    type="checkbox"
                    id="surfboards"
                    className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    checked={field.value}
                    onChange={field.onChange}
                  />
                )}
              />
              <label htmlFor="surfboards" className="text-sm text-muted-foreground">
                Surfboards <span className="font-medium">($20.00 usd per Surfboard)</span>
              </label>
            </div>
          </div>
        </div>

        {/* Child Seats */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Child Safety Seats</h4>

          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Baby Seat (0-1 years)</Label>
              <Controller
                name="additionalItems.babySeat"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Baby Seat (Free)" />
                    </SelectTrigger>
                    <SelectContent>
                      {[0,1,2,3].map(num => (
                        <SelectItem key={num} value={num.toString()}>
                          {num} {num === 1 ? 'Seat' : 'Seats'} (Free)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Car Seat (2-4 years)</Label>
              <Controller
                name="additionalItems.carSeat"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Car Seat (Free)" />
                    </SelectTrigger>
                    <SelectContent>
                      {[0,1,2,3].map(num => (
                        <SelectItem key={num} value={num.toString()}>
                          {num} {num === 1 ? 'Seat' : 'Seats'} (Free)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Booster Seat (5-8 years)</Label>
              <Controller
                name="additionalItems.boosterSeat"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Booster Seat (Free)" />
                    </SelectTrigger>
                    <SelectContent>
                      {[0,1,2,3].map(num => (
                        <SelectItem key={num} value={num.toString()}>
                          {num} {num === 1 ? 'Seat' : 'Seats'} (Free)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>
        </div>

        {/* Special Instructions */}
        <div className="space-y-2">
          <Label>Special Instructions (Optional)</Label>
          <Controller
            name="additionalItems.specialInstructions"
            control={control}
            render={({ field }) => (
              <Textarea
                placeholder="Is there something we should know?"
                className="min-h-[100px]"
                {...field}
              />
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default AdditionalItemsCard;
